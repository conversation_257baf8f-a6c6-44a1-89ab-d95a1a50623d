// Update this file after deploying the contract via Remix
// Replace the placeholder values with your actual deployed contract details

export const DEPLOYED_CONTRACT_ADDRESS = "0x0000000000000000000000000000000000000000"; // UPDATE THIS
export const DEPLOYMENT_BLOCK = 0; // UPDATE THIS

// Copy the ABI from Remix after compilation and paste it here
export const DEPLOYED_CONTRACT_ABI = [
  // UPDATE THIS WITH THE ACTUAL ABI FROM REMIX
  // The ABI will be available in the Solidity Compiler tab after compilation
  // It should look like this (but much longer):
  /*
  {
    "inputs": [],
    "name": "getTotalListings",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  ...
  */
] as const;

// Network configuration
export const NETWORK_CONFIG = {
  chainId: 84532, // Base Sepolia
  name: "Base Sepolia",
  rpcUrl: "https://sepolia.base.org",
  blockExplorer: "https://sepolia.basescan.org"
};

// Helper function to get contract URL on block explorer
export function getContractUrl(address: string): string {
  return `${NETWORK_CONFIG.blockExplorer}/address/${address}`;
}

// Helper function to get transaction URL on block explorer
export function getTransactionUrl(txHash: string): string {
  return `${NETWORK_CONFIG.blockExplorer}/tx/${txHash}`;
}
