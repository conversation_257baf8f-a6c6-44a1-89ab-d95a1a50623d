import { useState, useEffect } from 'react';
import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { 
  LandListing, 
  CreateListingParams, 
  LAND_LISTING_CONTRACT_ADDRESS, 
  LAND_LISTING_ABI 
} from '../contracts/LandListingContract';

// Mock data for development - replace with actual contract calls
const mockListings: LandListing[] = [
  {
    id: '1',
    owner: '******************************************',
    title: 'Premium Agricultural Land',
    location: 'Nakuru County, Kenya',
    size: 50,
    price: BigInt('1000000000000000000'), // 1 ETH in wei
    priceUnit: 'acre',
    status: 'available',
    description: 'Fertile agricultural land perfect for crop farming. Located in the heart of Nakuru County with excellent access to water and transportation.',
    features: ['Water Access', 'Road Access', 'Fertile Soil', 'Fenced'],
    createdAt: Date.now() - ********, // 1 day ago
    updatedAt: Date.now() - ********,
  },
  {
    id: '2',
    owner: '******************************************',
    title: 'Livestock Grazing Land',
    location: 'Kajiado County, Kenya',
    size: 100,
    price: BigInt('2000000000000000000'), // 2 ETH in wei
    priceUnit: 'acre',
    status: 'available',
    description: 'Expansive grazing land suitable for cattle and sheep farming. Natural water sources and established grazing patterns.',
    features: ['Natural Water', 'Grazing Rights', 'Large Area', 'Pastoral Community'],
    createdAt: Date.now() - 172800000, // 2 days ago
    updatedAt: Date.now() - 172800000,
  },
  {
    id: '3',
    owner: '******************************************',
    title: 'Mixed Farming Plot',
    location: 'Kiambu County, Kenya',
    size: 25,
    price: BigInt('1500000000000000000'), // 1.5 ETH in wei
    priceUnit: 'acre',
    status: 'pending',
    description: 'Versatile land suitable for both crop farming and small-scale livestock. Close to Nairobi markets.',
    features: ['Market Access', 'Mixed Use', 'Good Climate', 'Transport Links'],
    createdAt: Date.now() - *********, // 3 days ago
    updatedAt: Date.now() - ********,
  }
];

export const useLandListings = () => {
  const [listings, setListings] = useState<LandListing[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { address } = useAccount();
  const { writeContract } = useWriteContract();

  // For now, use mock data. In production, this would read from the smart contract
  useEffect(() => {
    const loadListings = async () => {
      setIsLoading(true);
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setListings(mockListings);
      setIsLoading(false);
    };

    loadListings();
  }, []);

  const createListing = async (params: CreateListingParams) => {
    if (!address) throw new Error('Wallet not connected');

    try {
      // In production, this would call the smart contract
      // await writeContract({
      //   address: LAND_LISTING_CONTRACT_ADDRESS,
      //   abi: LAND_LISTING_ABI,
      //   functionName: 'createListing',
      //   args: [
      //     params.title,
      //     params.location,
      //     BigInt(params.size),
      //     params.price,
      //     params.priceUnit,
      //     params.description,
      //     params.features
      //   ]
      // });

      // Mock implementation
      const newListing: LandListing = {
        id: (listings.length + 1).toString(),
        owner: address,
        ...params,
        status: 'available',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      setListings(prev => [newListing, ...prev]);
      return newListing.id;
    } catch (error) {
      console.error('Failed to create listing:', error);
      throw error;
    }
  };

  const requestLease = async (listingId: string) => {
    if (!address) throw new Error('Wallet not connected');

    try {
      // In production, this would call the smart contract
      // await writeContract({
      //   address: LAND_LISTING_CONTRACT_ADDRESS,
      //   abi: LAND_LISTING_ABI,
      //   functionName: 'requestLease',
      //   args: [BigInt(listingId)]
      // });

      // Mock implementation
      setListings(prev => 
        prev.map(listing => 
          listing.id === listingId 
            ? { ...listing, status: 'pending' as const, updatedAt: Date.now() }
            : listing
        )
      );
    } catch (error) {
      console.error('Failed to request lease:', error);
      throw error;
    }
  };

  const getUserListings = () => {
    return listings.filter(listing => listing.owner.toLowerCase() === address?.toLowerCase());
  };

  const getAvailableListings = () => {
    return listings.filter(listing => listing.status === 'available');
  };

  return {
    listings,
    isLoading,
    createListing,
    requestLease,
    getUserListings,
    getAvailableListings,
  };
};
